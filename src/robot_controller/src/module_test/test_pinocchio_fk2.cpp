#include "robot_controller/def_class.h"


int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_pin_fk2");
    ros::NodeHandle nh;
    
    ROS_INFO_STREAM("当前使用的 Pinocchio 版本" << PINOCCHIO_VERSION);

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model, false, true);

    std::cout << "=== 原始完整模型信息 ===" << std::endl;
    std::cout << "Total joints: " << model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << model.nv << std::endl;

    // 打印所有关节名称，便于了解模型结构
    std::cout << "\n所有关节列表:" << std::endl;
    pinocchio::JointIndex jid;
    for(int i = 0; i < model.njoints; i++){
        jid = model.getJointId(model.names[i]);
        std::cout << "joint " << i << ": " << model.names[i] << ", " << model.joints[i].nq() << " DOF" << std::endl;
    }

    // 构建只包含右臂的模型
    // 需要锁定（移除）左臂和其他不需要的关节，只保留右臂关节
    std::vector<std::string> list_of_joints_to_lock_by_name;

    // 添加要锁定的左臂关节
    list_of_joints_to_lock_by_name.push_back("l_joint1");
    list_of_joints_to_lock_by_name.push_back("l_joint2");
    list_of_joints_to_lock_by_name.push_back("l_joint3");
    list_of_joints_to_lock_by_name.push_back("l_joint4");
    list_of_joints_to_lock_by_name.push_back("l_joint4_mimic");
    list_of_joints_to_lock_by_name.push_back("l_joint5");
    list_of_joints_to_lock_by_name.push_back("l_joint6");
    list_of_joints_to_lock_by_name.push_back("l_joint7");

    // 添加要锁定的基座关节（如果不需要基座移动的话）
    list_of_joints_to_lock_by_name.push_back("base_joint1");
    list_of_joints_to_lock_by_name.push_back("base_joint2");

    // 添加要锁定的左手关节
    list_of_joints_to_lock_by_name.push_back("l_index_MCP_FE");
    list_of_joints_to_lock_by_name.push_back("l_index_MCP_AA");
    list_of_joints_to_lock_by_name.push_back("l_index_PIP");
    list_of_joints_to_lock_by_name.push_back("l_index_DIP");
    list_of_joints_to_lock_by_name.push_back("l_middle_MCP_FE");
    list_of_joints_to_lock_by_name.push_back("l_middle_MCP_AA");
    list_of_joints_to_lock_by_name.push_back("l_middle_PIP");
    list_of_joints_to_lock_by_name.push_back("l_middle_DIP");
    list_of_joints_to_lock_by_name.push_back("l_picky_MCP_FE");
    list_of_joints_to_lock_by_name.push_back("l_picky_MCP_AA");
    list_of_joints_to_lock_by_name.push_back("l_picky_PIP");
    list_of_joints_to_lock_by_name.push_back("l_picky_DIP");
    list_of_joints_to_lock_by_name.push_back("l_ring_MCP_FE");
    list_of_joints_to_lock_by_name.push_back("l_ring_MCP_AA");
    list_of_joints_to_lock_by_name.push_back("l_ring_PIP");
    list_of_joints_to_lock_by_name.push_back("l_ring_DIP");
    list_of_joints_to_lock_by_name.push_back("l_thumb_MCP_FE");
    list_of_joints_to_lock_by_name.push_back("l_thumb_MCP_AA");
    list_of_joints_to_lock_by_name.push_back("l_thumb_PIP");
    list_of_joints_to_lock_by_name.push_back("l_thumb_DIP");

    std::cout << "\n=== 准备锁定的关节 ===" << std::endl;
    // 将关节名称转换为关节ID
    std::vector<pinocchio::JointIndex> list_of_joints_to_lock_by_id;
    for (std::vector<std::string>::const_iterator it = list_of_joints_to_lock_by_name.begin();
        it != list_of_joints_to_lock_by_name.end(); ++it)
    {
        const std::string & joint_name = *it;
        if (model.existJointName(joint_name)) {
            list_of_joints_to_lock_by_id.push_back(model.getJointId(joint_name));
            std::cout << "将锁定关节: " << joint_name << std::endl;
        } else {
            std::cout << "关节 " << joint_name << " 不存在于模型中" << std::endl;
        }
    }

    // 生成随机配置用于锁定关节的位置
    Eigen::VectorXd q_rand = pinocchio::randomConfiguration(model);

    // 构建简化模型（只包含右臂）
    pinocchio::Model right_arm_model = pinocchio::buildReducedModel(model, list_of_joints_to_lock_by_id, q_rand);

    std::cout << "\n=== 右臂模型信息 ===" << std::endl;
    std::cout << "Total joints: " << right_arm_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << right_arm_model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << right_arm_model.nv << std::endl;

    std::cout << "\n右臂模型关节列表:" << std::endl;
    for(int i = 0; i < right_arm_model.njoints; i++){
        std::cout << "joint " << i << ": " << right_arm_model.names[i] << ", " << right_arm_model.joints[i].nq() << " DOF" << std::endl;
    }

    // 创建右臂模型的数据结构
    pinocchio::Data right_arm_data(right_arm_model);

    // 测试右臂模型的正运动学
    std::cout << "\n=== 测试右臂正运动学 ===" << std::endl;

    // 为右臂模型设置关节角度
    Eigen::VectorXd q_right(right_arm_model.nq);
    q_right.setZero();  // 初始化为零位置

    // 设置一些测试角度（根据右臂模型的实际关节数量）
    if(right_arm_model.nq >= 7) {
        // 假设前7个关节是右臂的7个关节
        q_right(0) = 0.308;  // r_joint1
        q_right(1) = 0.688;  // r_joint2
        q_right(2) = 0.295;  // r_joint3
        q_right(3) = 0.457;  // r_joint4
        q_right(4) = 0.161;  // r_joint5
        q_right(5) = 0.326;  // r_joint6
        q_right(6) = 0.598;  // r_joint7
    }

    std::cout << "设置的右臂关节角度: ";
    for(int i = 0; i < q_right.size(); i++) {
        std::cout << q_right(i) << " ";
    }
    std::cout << std::endl;

    // 查找右臂末端执行器frame
    std::string end_effector_name = "r_Link7";
    if(right_arm_model.existFrame(end_effector_name)) {
        pinocchio::FrameIndex fid = right_arm_model.getFrameId(end_effector_name);

        // 执行正运动学计算
        pinocchio::forwardKinematics(right_arm_model, right_arm_data, q_right);
        pinocchio::updateFramePlacements(right_arm_model, right_arm_data);

        // 获取末端执行器的变换矩阵
        pinocchio::SE3Tpl<double> X_ee = right_arm_data.oMf[fid];

        std::cout << "\n右臂末端执行器位姿:" << std::endl;
        std::cout << "位置 (x, y, z): " << X_ee.translation().transpose() << std::endl;
        std::cout << "旋转矩阵:\n" << X_ee.rotation() << std::endl;

        // 转换为4x4变换矩阵
        Eigen::Matrix4d X_matrix = Eigen::Matrix4d::Identity();
        X_matrix.block<3,3>(0,0) = X_ee.rotation();
        X_matrix.block<3,1>(0,3) = X_ee.translation();

        std::cout << "\n4x4变换矩阵:\n" << X_matrix << std::endl;

    } else {
        std::cout << "未找到右臂末端执行器frame: " << end_effector_name << std::endl;
        std::cout << "可用的frames:" << std::endl;
        for(int i = 0; i < right_arm_model.nframes; i++) {
            std::cout << "  " << right_arm_model.frames[i].name << std::endl;
        }
    }


    return 0;
}
